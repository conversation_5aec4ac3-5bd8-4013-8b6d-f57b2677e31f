# Formal Methodology for VM Deobfuscation

This document explains how to use the VMDeobf framework to formally verify the correctness of VM deobfuscation, implementing the methodology described in the research paper.

## Overview

The VMDeobf framework implements a novel approach that reframes deobfuscation from heuristic reverse engineering to formal proof and program verification. The key insight is that by constructing a precise mathematical model of the obfuscated VM's logic using advanced type systems, we can leverage automated reasoning to systematically reconstruct the original program's logic with formal correctness guarantees.

## Core Methodology

### 1. Path-Dependent Type Modeling (Section 2.2)

The framework uses path-dependent types to ensure VM state encapsulation:

```lean
-- VM instance with unique identifier
def myVM : VMId := ⟨42, 0x12345678⟩

-- Path-dependent state that belongs to this specific VM
def vmState : VMState myVM 8 := {
  registers := Vector.replicate 8 VMValue.nil,
  memory := fun _ => VMValue.nil,
  pc := 0,
  stack := [],
  call_stack := [],
  vm_instance := myVM
}
```

This prevents confusion between different VM instances at the type level, ensuring state integrity during formal analysis.

### 2. Dynamic Type System (Section 2.1)

JavaScript/Lua dynamic typing is modeled using sum types with precise coercion rules:

```lean
-- Dynamic values with type coercion
inductive VMValue where
  | number : Float → VMValue
  | string : String → VMValue  
  | boolean : Bool → VMValue
  | nil : VMValue
  | undefined : VMValue
  | object : List (String × VMValue) → VMValue
  | array : List VMValue → VMValue

-- Type coercion following JavaScript semantics
def coerceToNumber : VMValue → Float
def coerceToString : VMValue → String
def coerceToBoolean : VMValue → Bool
```

### 3. Denotational Semantics (Section 2.1)

Each VM instruction is given precise mathematical meaning through state transition functions:

```lean
-- Instruction semantics as state transitions
def instrSemantics (vm : VMId) (regCount : Nat) : 
  VInstruction vm → VMState vm regCount → ExecResult vm regCount
```

## Automated Proof Search (Section 3)

### 1. SMT Solver Integration

The framework integrates with high-performance SAT/SMT solvers:

```lean
-- SMT configuration for automated reasoning
def smtConfig : SMTConfig := {
  solver_name := "z3",
  timeout_ms := 10000,
  memory_limit_mb := 2048,
  logic := "QF_BV",
  parallel_mode := true,
  proof_generation := true,
  incremental_mode := false
}

-- Convert VM semantics to SMT query
def query := vmToSMT myVM instruction highLevelOp

-- Execute with external solver
let result ← executeSMT smtConfig query
```

### 2. Sledgehammer-Style Automation

Following Isabelle's Sledgehammer, the framework provides automated proof search:

```lean
-- Automated proof attempt
let proof ← proveSemanticEquiv myVM instruction highLevelOp ProofStrategy.hammer
```

### 3. Hybrid Formal-Dynamic Analysis

The framework combines formal methods with dynamic analysis:

```lean
-- Generate execution traces for proof hints
let traces ← executeWithTrace myVM program input

-- Use traces to guide automated proof search
let result ← automaticLifting handler traces liftingConfig
```

## Interpreter Semantics Testing (Section 4.2)

The framework implements black-box analysis inspired by LuaHunt:

```lean
-- Generate test gadgets for instruction discovery
let gadgets := generateTestGadgets

-- Execute gadgets and infer semantics
for gadget in gadgets do
  let result ← executeGadget vmBinary gadget
  match inferSemantics result with
  | some highLevelOp => 
      -- Found semantic mapping
      createLiftingCandidate gadget.target_opcode highLevelOp
  | none => continue
```

## Usage Examples

### Basic Instruction Verification

```lean
-- Define VM and instruction
def vm : VMId := ⟨1, 0x1234⟩
def addInstr : VInstruction vm := {
  opcode := Opcode.add,
  operands := [],
  vm_ref := vm
}

-- Prove semantic equivalence
theorem add_correctness :
  ∀ (state : VMState vm 4) (a b : VMValue),
    let input_state := { state with stack := [a, b] }
    match instrSemantics vm 4 addInstr input_state with
    | ExecResult.continue output_state =>
        match output_state.stack with
        | result :: _ => result = VMValue.number (coerceToNumber a + coerceToNumber b)
        | _ => False
    | _ => False := by
  -- Proof completed by automation framework
  sorry
```

### Complete Program Deobfuscation

```lean
-- Load obfuscated program
let obfuscated := loadVMProgram "obfuscated.bin"

-- Analyze VM structure
let vmStructure ← analyzeVMStructure obfuscated

-- Extract instruction semantics
let handlers ← extractHandlers obfuscated
let candidates ← extractLiftingCandidates handlers []

-- Perform automated lifting with formal verification
let results ← batchLifting handlers [] batchConfig

-- Generate deobfuscated program
let deobfuscated ← generateDeobfuscated results

-- Verify semantic equivalence
theorem deobfuscation_correct :
  programEquiv vm1 vm2 regCount obfuscated deobfuscated := by
  -- Automated proof using the framework
  sorry
```

## Integration with External Tools

### SMT Solvers

The framework supports multiple SMT solvers:

- **Z3**: Microsoft's SMT solver with excellent bit-vector support
- **CVC5**: Stanford's SMT solver with strong theory reasoning
- **CaDiCaL**: High-performance SAT solver for boolean problems
- **Vampire**: First-order theorem prover for complex logical reasoning

### Configuration

```bash
# Install solvers
./scripts/install-solvers.sh

# Configure solver preferences
cat > automation/solvers/config.json << EOF
{
  "default_solver": "z3",
  "timeout": 30000,
  "parallel_execution": true,
  "proof_generation": true
}
EOF
```

## Correctness Guarantees

The framework provides several levels of correctness guarantees:

1. **Type Safety**: Path-dependent types prevent VM state confusion
2. **Memory Safety**: Dependent types ensure bounds checking
3. **Semantic Equivalence**: Formal proofs of deobfuscation correctness
4. **Termination**: Verification of program termination properties

## Performance Considerations

- **Parallel Solving**: Multiple solvers run in parallel for faster results
- **Incremental Verification**: Reuse previous proof results
- **Proof Caching**: Cache successful proofs for similar patterns
- **Timeout Management**: Configurable timeouts prevent infinite search

## Limitations and Future Work

Current limitations:
- Manual VM structure modeling (being automated)
- Limited to stack-based VMs (register VMs planned)
- Requires network access for Mathlib dependencies

Future enhancements:
- LLM-assisted proof generation
- Automated model generation from binaries
- Support for more complex obfuscation techniques
- Integration with existing reverse engineering tools

## Getting Started

1. **Install Dependencies**:
   ```bash
   ./scripts/setup.sh
   ```

2. **Build the Framework**:
   ```bash
   lake build
   ```

3. **Run Examples**:
   ```bash
   lake exe vmdeobf analyze examples/js/simple-vm/obfuscated.js
   lake exe vmdeobf deobfuscate examples/js/simple-vm/obfuscated.js output.js
   ```

4. **Verify Results**:
   ```bash
   lake exe vmdeobf verify examples/js/simple-vm/original.js output.js
   ```

For more detailed examples, see the `examples/formal-verification/` directory.
