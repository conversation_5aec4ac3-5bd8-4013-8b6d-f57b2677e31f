#!/bin/bash

# Install SMT/SAT solvers for automated theorem proving
# This script installs the external solvers used by the automation framework

set -e

echo "Installing SMT/SAT solvers..."

# Detect platform
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    PLATFORM="linux"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    PLATFORM="macos"
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
    PLATFORM="windows"
else
    echo "Unsupported platform: $OSTYPE"
    exit 1
fi

# Create solvers directory
mkdir -p automation/solvers/bin

# Install Z3 SMT solver
echo "Installing Z3..."
if [[ "$PLATFORM" == "linux" ]]; then
    if command -v apt-get &> /dev/null; then
        sudo apt-get update
        sudo apt-get install -y z3
    elif command -v yum &> /dev/null; then
        sudo yum install -y z3
    elif command -v pacman &> /dev/null; then
        sudo pacman -S z3
    else
        # Download binary release
        wget https://github.com/Z3Prover/z3/releases/download/z3-4.12.2/z3-4.12.2-x64-glibc-2.31.zip
        unzip z3-4.12.2-x64-glibc-2.31.zip
        cp z3-4.12.2-x64-glibc-2.31/bin/z3 automation/solvers/bin/
        rm -rf z3-4.12.2-x64-glibc-2.31*
    fi
elif [[ "$PLATFORM" == "macos" ]]; then
    if command -v brew &> /dev/null; then
        brew install z3
    else
        # Download binary release
        wget https://github.com/Z3Prover/z3/releases/download/z3-4.12.2/z3-4.12.2-x64-osx-10.16.zip
        unzip z3-4.12.2-x64-osx-10.16.zip
        cp z3-4.12.2-x64-osx-10.16/bin/z3 automation/solvers/bin/
        rm -rf z3-4.12.2-x64-osx-10.16*
    fi
elif [[ "$PLATFORM" == "windows" ]]; then
    echo "Please install Z3 manually from https://github.com/Z3Prover/z3/releases"
fi

# Install CVC5 SMT solver
echo "Installing CVC5..."
if [[ "$PLATFORM" == "linux" ]]; then
    wget https://github.com/cvc5/cvc5/releases/download/cvc5-1.0.8/cvc5-Linux
    chmod +x cvc5-Linux
    mv cvc5-Linux automation/solvers/bin/cvc5
elif [[ "$PLATFORM" == "macos" ]]; then
    wget https://github.com/cvc5/cvc5/releases/download/cvc5-1.0.8/cvc5-macOS
    chmod +x cvc5-macOS
    mv cvc5-macOS automation/solvers/bin/cvc5
elif [[ "$PLATFORM" == "windows" ]]; then
    echo "Please install CVC5 manually from https://github.com/cvc5/cvc5/releases"
fi

# Install CaDiCaL SAT solver
echo "Installing CaDiCaL..."
if [[ "$PLATFORM" == "linux" ]] || [[ "$PLATFORM" == "macos" ]]; then
    # Build from source
    if command -v git &> /dev/null && command -v make &> /dev/null && command -v g++ &> /dev/null; then
        git clone https://github.com/arminbiere/cadical.git
        cd cadical
        ./configure
        make
        cp build/cadical ../automation/solvers/bin/
        cd ..
        rm -rf cadical
    else
        echo "Warning: Could not build CaDiCaL (missing git, make, or g++)"
    fi
elif [[ "$PLATFORM" == "windows" ]]; then
    echo "Please install CaDiCaL manually or use WSL"
fi

# Install Vampire theorem prover (for Sledgehammer-style automation)
echo "Installing Vampire..."
if [[ "$PLATFORM" == "linux" ]]; then
    wget https://github.com/vprover/vampire/releases/download/v4.7/vampire_linux64_rel_static_HEAD_6294
    chmod +x vampire_linux64_rel_static_HEAD_6294
    mv vampire_linux64_rel_static_HEAD_6294 automation/solvers/bin/vampire
elif [[ "$PLATFORM" == "macos" ]]; then
    wget https://github.com/vprover/vampire/releases/download/v4.7/vampire_macos_rel_HEAD_6294
    chmod +x vampire_macos_rel_HEAD_6294
    mv vampire_macos_rel_HEAD_6294 automation/solvers/bin/vampire
elif [[ "$PLATFORM" == "windows" ]]; then
    echo "Please install Vampire manually from https://github.com/vprover/vampire/releases"
fi

# Create solver configuration
echo "Creating solver configuration..."
cat > automation/solvers/config.json << EOF
{
  "solvers": {
    "z3": {
      "path": "z3",
      "timeout": 30000,
      "memory_limit": 4096,
      "supported_logics": ["QF_BV", "QF_LIA", "QF_NIA", "QF_LRA", "QF_UFBV"]
    },
    "cvc5": {
      "path": "automation/solvers/bin/cvc5",
      "timeout": 30000,
      "memory_limit": 4096,
      "supported_logics": ["QF_BV", "QF_LIA", "QF_NIA", "QF_LRA", "QF_UFBV"]
    },
    "cadical": {
      "path": "automation/solvers/bin/cadical",
      "timeout": 30000,
      "memory_limit": 2048,
      "supported_logics": ["SAT"]
    },
    "vampire": {
      "path": "automation/solvers/bin/vampire",
      "timeout": 30000,
      "memory_limit": 4096,
      "supported_logics": ["FOL"]
    }
  },
  "default_solver": "z3",
  "parallel_execution": true,
  "max_parallel_jobs": 4
}
EOF

# Test solver installations
echo "Testing solver installations..."
if command -v z3 &> /dev/null; then
    echo "✓ Z3 installed successfully"
else
    echo "✗ Z3 not found in PATH"
fi

if [ -f "automation/solvers/bin/cvc5" ]; then
    echo "✓ CVC5 installed successfully"
else
    echo "✗ CVC5 not installed"
fi

if [ -f "automation/solvers/bin/cadical" ]; then
    echo "✓ CaDiCaL installed successfully"
else
    echo "✗ CaDiCaL not installed"
fi

if [ -f "automation/solvers/bin/vampire" ]; then
    echo "✓ Vampire installed successfully"
else
    echo "✗ Vampire not installed"
fi

echo "Solver installation complete!"
echo "Configuration saved to automation/solvers/config.json"
