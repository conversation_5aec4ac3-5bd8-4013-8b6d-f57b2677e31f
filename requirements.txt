# Python dependencies for VMDeobf analysis tools
# These tools complement the formal verification framework

# Core analysis libraries
numpy>=1.21.0
scipy>=1.7.0
matplotlib>=3.4.0
pandas>=1.3.0

# Binary analysis and reverse engineering
capstone>=4.0.2          # Disassembly framework
keystone-engine>=0.9.2   # Assembly framework
unicorn>=2.0.0           # CPU emulator framework
pefile>=2021.9.3         # PE file analysis
pyelftools>=0.27         # ELF file analysis

# Machine learning for pattern recognition
scikit-learn>=1.0.0
tensorflow>=2.8.0
torch>=1.11.0

# Graph analysis for control flow
networkx>=2.6.0
pygraphviz>=1.7

# SMT solver Python bindings
z3-solver>=********

# Symbolic execution
angr>=9.2.0

# Dynamic analysis and instrumentation
frida>=15.0.0
pwntools>=4.7.0

# Fuzzing and test case generation
boofuzz>=0.4.0

# Visualization and reporting
plotly>=5.0.0
seaborn>=0.11.0
graphviz>=0.19.0

# Configuration and CLI
click>=8.0.0
pyyaml>=6.0
toml>=0.10.0
rich>=12.0.0

# Testing and development
pytest>=6.2.0
pytest-cov>=3.0.0
black>=22.0.0
flake8>=4.0.0
mypy>=0.910

# Jupyter notebooks for research
jupyter>=1.0.0
ipywidgets>=7.6.0

# Performance profiling
py-spy>=0.3.0
memory-profiler>=0.60.0

# Parallel processing
joblib>=1.1.0
multiprocessing-logging>=0.3.0
