// Original JavaScript code before obfuscation
// Simple arithmetic and control flow example

function calculateSum(a, b) {
    if (a > 0 && b > 0) {
        return a + b;
    } else {
        return 0;
    }
}

function factorial(n) {
    if (n <= 1) {
        return 1;
    } else {
        return n * factorial(n - 1);
    }
}

// Main execution
let x = 5;
let y = 3;
let sum = calculateSum(x, y);
let fact = factorial(4);

console.log("Sum:", sum);
console.log("Factorial:", fact);

// Expected output:
// Sum: 8
// Factorial: 24
