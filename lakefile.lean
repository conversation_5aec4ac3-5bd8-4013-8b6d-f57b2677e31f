import Lake
open Lake DSL

package «vmdeobf» where
  version := v!"0.1.0"
  keywords := #["formal-verification", "deobfuscation", "virtual-machines"]
  description := "A formally verified approach to deobfuscating virtualized code"

require mathlib from git
  "https://github.com/leanprover-community/mathlib4.git"

require std from git
  "https://github.com/leanprover/std4" @ "main"

@[default_target]
lean_lib «VMDeobf» where
  -- add library configuration options here

lean_lib «VMDeobf.Core» where
  -- Core formal verification components

lean_lib «VMDeobf.Analysis» where
  -- VM analysis and reverse engineering

lean_lib «VMDeobf.Automation» where
  -- Automated theorem proving infrastructure

lean_exe «vmdeobf» where
  root := `Main
  -- add executable configuration options here
