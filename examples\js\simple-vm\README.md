# Simple VM Obfuscation Example

This example demonstrates a basic virtual machine obfuscation technique applied to JavaScript code.

## Overview

The original JavaScript code contains two simple functions:
1. `calculateSum(a, b)` - Adds two positive numbers, returns 0 otherwise
2. `factorial(n)` - Calculates factorial recursively

The obfuscated version replaces this high-level code with:
- A custom virtual machine with 16 registers and a stack
- A custom instruction set with 14 opcodes
- Bytecode that implements the original logic
- An interpreter loop that executes the bytecode

## Obfuscation Techniques Used

### 1. Virtual Machine Architecture
- **Registers**: 16 virtual registers for data storage
- **Stack**: Stack-based computation model
- **Memory**: Associative array for complex data
- **Program Counter**: Tracks execution position

### 2. Custom Instruction Set
```
LOAD_CONST  0x01  - Push constant onto stack
LOAD_REG    0x02  - Push register value onto stack  
STORE_REG   0x03  - Pop stack value into register
ADD         0x04  - Pop two values, push sum
SUB         0x05  - Pop two values, push difference
MUL         0x06  - Pop two values, push product
CMP_GT      0x07  - Pop two values, push comparison result
CMP_LE      0x08  - Pop two values, push comparison result
JUMP        0x09  - Unconditional jump
JUMP_IF     0x0A  - Conditional jump
CALL        0x0B  - Function call
RET         0x0C  - Function return
PRINT       0x0D  - Output value
HALT        0x0E  - Stop execution
```

### 3. Semantic Obfuscation
- Bit manipulation in instruction handlers (`val ^ 0x5A5A5A5A ^ 0x5A5A5A5A`)
- Register masking (`reg & 0xF`)
- Value truncation (`& 0xFFFFFFFF`)

## Analysis Challenges

This obfuscation presents several challenges for traditional analysis:

1. **Semantic Gap**: High-level operations are replaced with low-level VM instructions
2. **Indirect Control Flow**: Program flow goes through VM interpreter
3. **Dynamic Dispatch**: Instruction handlers are called indirectly
4. **State Encapsulation**: Program state is hidden in VM registers

## Expected Deobfuscation Process

The VMDeobf framework should:

1. **Identify VM Structure**: Detect the virtual machine components
2. **Extract Instruction Semantics**: Analyze each opcode handler
3. **Reconstruct Control Flow**: Map bytecode to high-level control structures
4. **Lift Operations**: Convert VM instructions back to JavaScript operations
5. **Verify Equivalence**: Prove semantic equivalence with original

## Formal Verification Goals

The framework should prove:

```lean
theorem deobfuscation_correct : 
  ∀ (input : List VMValue), 
    execute_original input = execute_deobfuscated input
```

Where:
- `execute_original` represents the semantics of the original code
- `execute_deobfuscated` represents the semantics of the deobfuscated result

## Test Cases

Input values for testing semantic equivalence:

```json
{
  "test_cases": [
    {
      "name": "positive_numbers",
      "inputs": {"a": 5, "b": 3},
      "expected_sum": 8,
      "expected_factorial_4": 24
    },
    {
      "name": "zero_values", 
      "inputs": {"a": 0, "b": 5},
      "expected_sum": 0,
      "expected_factorial_4": 24
    },
    {
      "name": "negative_values",
      "inputs": {"a": -2, "b": 3}, 
      "expected_sum": 0,
      "expected_factorial_4": 24
    }
  ]
}
```

## Running This Example

```bash
# Analyze the obfuscated code
lake exe vmdeobf analyze examples/js/simple-vm/obfuscated.js

# Perform deobfuscation
lake exe vmdeobf deobfuscate examples/js/simple-vm/obfuscated.js output/simple-clean.js

# Verify semantic equivalence
lake exe vmdeobf verify examples/js/simple-vm/original.js output/simple-clean.js
```

## Research Notes

This example is designed to test:
- Basic VM detection algorithms
- Instruction semantic extraction
- Control flow reconstruction
- Arithmetic operation lifting
- Function call/return handling

The obfuscation is intentionally simple to serve as a baseline for the formal verification framework.
