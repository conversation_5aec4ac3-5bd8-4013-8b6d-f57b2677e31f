#!/bin/bash

# VMDeobf Development Environment Setup Script
# This script sets up the complete development environment for the project

set -e

echo "Setting up VMDeobf development environment..."

# Check if we're on a supported platform
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    PLATFORM="linux"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    PLATFORM="macos"
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
    PLATFORM="windows"
else
    echo "Unsupported platform: $OSTYPE"
    exit 1
fi

echo "Detected platform: $PLATFORM"

# Install Lean 4 if not present
if ! command -v lean &> /dev/null; then
    echo "Installing Lean 4..."
    if [[ "$PLATFORM" == "linux" ]] || [[ "$PLATFORM" == "macos" ]]; then
        curl https://raw.githubusercontent.com/leanprover/elan/master/elan-init.sh -sSf | sh
        source ~/.profile
    elif [[ "$PLATFORM" == "windows" ]]; then
        echo "Please install Lean 4 manually from https://leanprover.github.io/lean4/doc/setup.html"
        exit 1
    fi
else
    echo "Lean 4 already installed"
fi

# Install Lake (Lean package manager) if not present
if ! command -v lake &> /dev/null; then
    echo "Installing Lake..."
    # Lake comes with Lean 4, so this should not be needed
    echo "Lake should be installed with Lean 4"
fi

# Install Python dependencies for analysis tools
echo "Installing Python dependencies..."
if command -v python3 &> /dev/null; then
    python3 -m pip install --user -r requirements.txt
elif command -v python &> /dev/null; then
    python -m pip install --user -r requirements.txt
else
    echo "Python not found. Please install Python 3.9+ manually."
    exit 1
fi

# Install SMT solvers
echo "Installing SMT solvers..."
./scripts/install-solvers.sh

# Set up Git hooks
echo "Setting up Git hooks..."
if [ -d ".git" ]; then
    cp scripts/pre-commit .git/hooks/
    chmod +x .git/hooks/pre-commit
    echo "Git pre-commit hook installed"
fi

# Create necessary directories
echo "Creating project directories..."
mkdir -p examples/js
mkdir -p examples/lua
mkdir -p examples/python
mkdir -p tests/unit
mkdir -p tests/integration
mkdir -p tests/formal
mkdir -p docs/api
mkdir -p tools/bin
mkdir -p analysis/static
mkdir -p analysis/dynamic
mkdir -p analysis/hybrid
mkdir -p automation/solvers
mkdir -p automation/tactics
mkdir -p automation/synthesis

# Download Mathlib if needed
echo "Setting up Mathlib..."
lake update

# Build the project to verify setup
echo "Building project to verify setup..."
lake build

echo ""
echo "Setup complete! 🎉"
echo ""
echo "Next steps:"
echo "1. Run 'make test' to verify everything works"
echo "2. Check out the examples in the examples/ directory"
echo "3. Read the documentation in docs/"
echo "4. Start with 'lake exe vmdeobf help' to see available commands"
echo ""
echo "For development:"
echo "- Use 'lake build' to build the project"
echo "- Use 'lake exe vmdeobf' to run the tool"
echo "- Use 'make check' to verify proofs"
echo "- Use 'make format' to format code"
