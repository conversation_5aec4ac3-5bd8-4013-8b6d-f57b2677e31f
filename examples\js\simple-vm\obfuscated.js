// VM-obfuscated version of the original JavaScript code
// This demonstrates a simple virtual machine obfuscation technique

(function() {
    // Virtual machine state
    var vm = {
        regs: new Array(16).fill(0),  // 16 virtual registers
        stack: [],
        memory: {},
        pc: 0,  // program counter
        code: [],
        running: true
    };

    // Virtual instruction set
    var opcodes = {
        LOAD_CONST: 0x01,
        LOAD_REG: 0x02,
        STORE_REG: 0x03,
        ADD: 0x04,
        SUB: 0x05,
        MUL: 0x06,
        CMP_GT: 0x07,
        CMP_LE: 0x08,
        JUMP: 0x09,
        JUMP_IF: 0x0A,
        CALL: 0x0B,
        RET: 0x0C,
        PRINT: 0x0D,
        HALT: 0x0E
    };

    // Instruction handlers (obfuscated with bit manipulation)
    var handlers = {};
    handlers[opcodes.LOAD_CONST] = function(val) {
        vm.stack.push(val ^ 0x5A5A5A5A ^ 0x5A5A5A5A);
        vm.pc++;
    };
    
    handlers[opcodes.LOAD_REG] = function(reg) {
        vm.stack.push(vm.regs[reg & 0xF]);
        vm.pc++;
    };
    
    handlers[opcodes.STORE_REG] = function(reg) {
        vm.regs[reg & 0xF] = vm.stack.pop();
        vm.pc++;
    };
    
    handlers[opcodes.ADD] = function() {
        var b = vm.stack.pop();
        var a = vm.stack.pop();
        vm.stack.push((a + b) & 0xFFFFFFFF);
        vm.pc++;
    };
    
    handlers[opcodes.MUL] = function() {
        var b = vm.stack.pop();
        var a = vm.stack.pop();
        vm.stack.push((a * b) & 0xFFFFFFFF);
        vm.pc++;
    };
    
    handlers[opcodes.CMP_GT] = function() {
        var b = vm.stack.pop();
        var a = vm.stack.pop();
        vm.stack.push(a > b ? 1 : 0);
        vm.pc++;
    };
    
    handlers[opcodes.CMP_LE] = function() {
        var b = vm.stack.pop();
        var a = vm.stack.pop();
        vm.stack.push(a <= b ? 1 : 0);
        vm.pc++;
    };
    
    handlers[opcodes.JUMP_IF] = function(addr) {
        if (vm.stack.pop()) {
            vm.pc = addr;
        } else {
            vm.pc++;
        }
    };
    
    handlers[opcodes.JUMP] = function(addr) {
        vm.pc = addr;
    };
    
    handlers[opcodes.PRINT] = function(msg) {
        console.log(msg, vm.stack.pop());
        vm.pc++;
    };
    
    handlers[opcodes.HALT] = function() {
        vm.running = false;
    };

    // Obfuscated bytecode representing the original program
    vm.code = [
        // calculateSum function (starts at address 0)
        [opcodes.LOAD_REG, 0],      // load a
        [opcodes.LOAD_CONST, 0],    // load 0
        [opcodes.CMP_GT],           // a > 0
        [opcodes.JUMP_IF, 8],       // if true, jump to next check
        [opcodes.LOAD_CONST, 0],    // else return 0
        [opcodes.STORE_REG, 2],     // store result
        [opcodes.JUMP, 18],         // jump to end
        
        [opcodes.LOAD_REG, 1],      // load b
        [opcodes.LOAD_CONST, 0],    // load 0  
        [opcodes.CMP_GT],           // b > 0
        [opcodes.JUMP_IF, 15],      // if true, do addition
        [opcodes.LOAD_CONST, 0],    // else return 0
        [opcodes.STORE_REG, 2],     // store result
        [opcodes.JUMP, 18],         // jump to end
        
        [opcodes.LOAD_REG, 0],      // load a
        [opcodes.LOAD_REG, 1],      // load b
        [opcodes.ADD],              // a + b
        [opcodes.STORE_REG, 2],     // store result
        
        // factorial function (starts at address 19)
        [opcodes.LOAD_REG, 3],      // load n
        [opcodes.LOAD_CONST, 1],    // load 1
        [opcodes.CMP_LE],           // n <= 1
        [opcodes.JUMP_IF, 26],      // if true, return 1
        [opcodes.LOAD_REG, 3],      // load n
        [opcodes.LOAD_REG, 3],      // load n
        [opcodes.LOAD_CONST, 1],    // load 1
        [opcodes.SUB],              // n - 1
        [opcodes.STORE_REG, 3],     // recursive call setup
        // ... (simplified for brevity)
        
        // Main execution
        [opcodes.LOAD_CONST, 5],    // x = 5
        [opcodes.STORE_REG, 0],
        [opcodes.LOAD_CONST, 3],    // y = 3  
        [opcodes.STORE_REG, 1],
        
        // Call calculateSum
        [opcodes.JUMP, 0],          // call calculateSum
        [opcodes.PRINT, "Sum:"],    // print result
        
        [opcodes.LOAD_CONST, 4],    // factorial(4)
        [opcodes.STORE_REG, 3],
        [opcodes.JUMP, 19],         // call factorial
        [opcodes.PRINT, "Factorial:"], // print result
        
        [opcodes.HALT]
    ];

    // VM execution loop (obfuscated)
    function execute() {
        while (vm.running && vm.pc < vm.code.length) {
            var instr = vm.code[vm.pc];
            var opcode = instr[0];
            var operand = instr[1];
            
            // Obfuscated dispatch
            var handler = handlers[opcode];
            if (handler) {
                handler(operand);
            } else {
                throw new Error("Unknown opcode: " + opcode);
            }
        }
    }

    // Start execution
    execute();
})();
