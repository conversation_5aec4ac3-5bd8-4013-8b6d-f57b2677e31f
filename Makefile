# VMDeobf Makefile
# Build system for the formally verified VM deobfuscation project

.PHONY: all build clean test docs install deps check format

# Default target
all: build

# Build the project
build:
	@echo "Building VMDeobf..."
	lake build

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	lake clean
	rm -rf .lake build

# Run tests
test: build
	@echo "Running tests..."
	lake test
	@echo "Running formal verification tests..."
	lake exe vmdeobf test

# Generate documentation
docs:
	@echo "Generating documentation..."
	lake build VMDeobf:docs

# Install dependencies
deps:
	@echo "Installing dependencies..."
	lake update
	@echo "Installing external tools..."
	./scripts/install-solvers.sh

# Type checking and linting
check:
	@echo "Type checking..."
	lake build --no-build
	@echo "Checking proofs..."
	lake exe lean --check VMDeobf

# Format code
format:
	@echo "Formatting Lean code..."
	find . -name "*.lean" -exec lake exe lean --format {} \;

# Install the tool
install: build
	@echo "Installing vmdeobf..."
	cp .lake/build/bin/vmdeobf /usr/local/bin/

# Development setup
setup:
	@echo "Setting up development environment..."
	./scripts/setup.sh

# Run specific examples
example-js:
	@echo "Running JavaScript obfuscation example..."
	lake exe vmdeobf analyze examples/js-obfuscated.bin
	lake exe vmdeobf deobfuscate examples/js-obfuscated.bin examples/js-deobfuscated.js

example-lua:
	@echo "Running Lua obfuscation example..."
	lake exe vmdeobf analyze examples/lua-obfuscated.bin
	lake exe vmdeobf deobfuscate examples/lua-obfuscated.bin examples/lua-deobfuscated.lua

# Benchmark performance
benchmark:
	@echo "Running performance benchmarks..."
	./scripts/benchmark.sh

# Help
help:
	@echo "VMDeobf Build System"
	@echo ""
	@echo "Available targets:"
	@echo "  build     - Build the project"
	@echo "  clean     - Clean build artifacts"
	@echo "  test      - Run all tests"
	@echo "  docs      - Generate documentation"
	@echo "  deps      - Install dependencies"
	@echo "  check     - Type check and verify proofs"
	@echo "  format    - Format source code"
	@echo "  install   - Install the tool system-wide"
	@echo "  setup     - Set up development environment"
	@echo "  help      - Show this help message"
