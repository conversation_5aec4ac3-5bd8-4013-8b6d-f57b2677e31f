# Lean build artifacts
.lake/
build/
*.olean

# Lake configuration
lake-manifest.json

# IDE files
.vscode/
*.swp
*.swo
*~

# Python artifacts
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.pytest_cache/
.coverage
htmlcov/

# Jupyter Notebook
.ipynb_checkpoints

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Solver binaries and temporary files
automation/solvers/bin/
*.smt2
*.cnf
*.log

# Analysis outputs
analysis_reports/
output/
benchmarks/results/
*.trace

# Temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db

# Documentation build
docs/_build/
docs/api/_build/

# Test artifacts
test_results/
coverage_reports/

# Research data
data/samples/
data/traces/
experiments/results/
