# VMDeobf Examples

This directory contains example obfuscated programs and test cases for demonstrating the VMDeobf framework.

## Directory Structure

```
examples/
├── js/                 # JavaScript obfuscation examples
├── lua/                # Lua obfuscation examples  
├── python/             # Python obfuscation examples
├── synthetic/          # Synthetic test cases
└── real-world/         # Real-world obfuscated samples
```

## Example Categories

### 1. JavaScript Obfuscation

- **js/simple-vm.js** - Basic JavaScript VM obfuscation
- **js/string-encryption.js** - String encryption with VM
- **js/control-flow.js** - Control flow obfuscation
- **js/anti-debug.js** - Anti-debugging techniques

### 2. Lua Obfuscation

- **lua/bytecode-vm.lua** - Custom Lua bytecode VM
- **lua/table-dispatch.lua** - Table-based instruction dispatch
- **lua/metamethod.lua** - Metamethod-based obfuscation

### 3. Synthetic Test Cases

- **synthetic/arithmetic.vm** - Pure arithmetic operations
- **synthetic/branching.vm** - Conditional branching
- **synthetic/loops.vm** - Loop constructs
- **synthetic/functions.vm** - Function calls and returns

## Running Examples

### Analyze VM Structure

```bash
# Analyze a JavaScript obfuscated file
lake exe vmdeobf analyze examples/js/simple-vm.js

# Analyze a Lua obfuscated file  
lake exe vmdeobf analyze examples/lua/bytecode-vm.lua
```

### Perform Deobfuscation

```bash
# Deobfuscate JavaScript
lake exe vmdeobf deobfuscate examples/js/simple-vm.js output/simple-clean.js

# Deobfuscate Lua
lake exe vmdeobf deobfuscate examples/lua/bytecode-vm.lua output/bytecode-clean.lua
```

### Verify Semantic Equivalence

```bash
# Verify that deobfuscation preserves semantics
lake exe vmdeobf verify examples/js/original.js output/simple-clean.js
```

## Test Case Format

Each example includes:

1. **Original source** - The unobfuscated program
2. **Obfuscated version** - The VM-obfuscated program  
3. **Expected output** - What the deobfuscated result should look like
4. **Test inputs** - Input values for testing semantic equivalence
5. **Analysis notes** - Documentation of the obfuscation technique

## Creating New Examples

To add a new example:

1. Create the directory structure:
   ```bash
   mkdir -p examples/new-category/example-name
   ```

2. Add the required files:
   - `original.ext` - Original source code
   - `obfuscated.ext` - Obfuscated version
   - `expected.ext` - Expected deobfuscated result
   - `test-inputs.json` - Test input values
   - `README.md` - Description and analysis notes

3. Update the test suite in `tests/integration/` to include the new example.

## Benchmark Results

Performance benchmarks for each example are tracked in `benchmarks/results.json`:

- Analysis time
- Deobfuscation time  
- Verification time
- Memory usage
- Success rate

Run benchmarks with:
```bash
make benchmark
```

## Contributing Examples

When contributing new examples:

1. Ensure the obfuscation technique is novel or demonstrates a specific challenge
2. Include comprehensive documentation
3. Provide test cases that cover edge cases
4. Verify that the formal verification succeeds
5. Add appropriate citations if based on published research

## Research Applications

These examples support several research directions:

- **Evaluation** - Measuring deobfuscation effectiveness
- **Benchmarking** - Comparing with other tools
- **Algorithm Development** - Testing new formal methods
- **Case Studies** - Analyzing real-world obfuscation patterns
